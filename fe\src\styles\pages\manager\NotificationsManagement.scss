@use "sass:color";
@use "../../base/variables" as vars;

.notifications-management {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;

  .notifications-content {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    transition: margin-left 0.3s ease;

    .page-header {
      margin-bottom: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 1rem;

      div {
        h1 {
          color: vars.$primary-color;
          margin-bottom: 0.5rem;
          font-size: 2rem;
          font-weight: 600;
        }

        p {
          color: #666;
          font-size: 1.1rem;
          margin: 0;
        }
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &.btn-primary {
          background: vars.$primary-color;
          color: white;

          &:hover {
            background: color.adjust(vars.$primary-color, $lightness: -10%);
            transform: translateY(-2px);
          }
        }
      }
    }

    .filters-section {
      background: white;
      padding: 1.5rem;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;

      .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        label {
          font-weight: 600;
          color: #333;
          font-size: 0.9rem;
        }

        select {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          min-width: 150px;

          &:focus {
            outline: none;
            border-color: vars.$primary-color;
            box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
          }
        }
      }
    }

    .notifications-list {
      margin-bottom: 2rem;

      .notification-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
        overflow: hidden;
        border-left: 4px solid transparent;

        &.info {
          border-left-color: #17a2b8;
        }

        &.warning {
          border-left-color: #ffc107;
        }

        &.danger {
          border-left-color: #dc3545;
        }

        &.primary {
          border-left-color: vars.$primary-color;
        }

        &.success {
          border-left-color: #28a745;
        }

        .card-header {
          padding: 1.5rem;
          background: #f8f9fa;
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: 1rem;

          .notification-info {
            flex: 1;

            .notification-title {
              margin: 0 0 1rem 0;
              color: #333;
              font-size: 1.2rem;
              font-weight: 600;
            }

            .notification-meta {
              display: flex;
              gap: 1rem;
              flex-wrap: wrap;
              align-items: center;

              .type-badge,
              .status-badge {
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.8rem;
                font-weight: 600;
                text-transform: uppercase;
              }

              .type-badge {
                &.info {
                  background: #d1ecf1;
                  color: #0c5460;
                }

                &.warning {
                  background: #fff3cd;
                  color: #856404;
                }

                &.danger {
                  background: #f8d7da;
                  color: #721c24;
                }

                &.primary {
                  background: rgba(vars.$primary-color, 0.1);
                  color: vars.$primary-color;
                }

                &.success {
                  background: #d4edda;
                  color: #155724;
                }
              }

              .status-badge {
                &.warning {
                  background: #fff3cd;
                  color: #856404;
                }

                &.success {
                  background: #d4edda;
                  color: #155724;
                }

                &.info {
                  background: #d1ecf1;
                  color: #0c5460;
                }

                &.danger {
                  background: #f8d7da;
                  color: #721c24;
                }
              }

              .recipient-info {
                color: #666;
                font-size: 0.9rem;
              }
            }
          }

          .notification-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;

            .btn {
              padding: 0.25rem 0.75rem;
              border: none;
              border-radius: 4px;
              font-size: 0.8rem;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.3s ease;

              &.btn-sm {
                padding: 0.25rem 0.75rem;
              }

              &.btn-success {
                background: #28a745;
                color: white;

                &:hover {
                  background: #218838;
                }
              }

              &.btn-danger {
                background: #dc3545;
                color: white;

                &:hover {
                  background: #c82333;
                }
              }
            }
          }
        }

        .card-body {
          padding: 1.5rem;

          .notification-message {
            color: #333;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            font-size: 1rem;
          }

          .notification-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;

            .stat-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0.5rem 0;
              border-bottom: 1px solid #f8f9fa;

              &:last-child {
                border-bottom: none;
              }

              .stat-label {
                color: #666;
                font-size: 0.9rem;
                font-weight: 500;
              }

              .stat-value {
                color: #333;
                font-weight: 600;
                font-size: 0.9rem;
              }
            }
          }
        }
      }
    }

    .notification-stats-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;

      .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;

        h3 {
          margin: 0 0 1rem 0;
          color: #666;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .stat-number {
          font-size: 2rem;
          font-weight: 700;
          margin: 0;
          color: vars.$primary-color;

          &.warning {
            color: #ffc107;
          }

          &.success {
            color: #28a745;
          }

          &.danger {
            color: #dc3545;
          }
        }
      }
    }
  }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    .modal-header {
      padding: 1.5rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        color: vars.$primary-color;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;

        &:hover {
          color: #333;
        }
      }
    }

    .modal-body {
      padding: 1.5rem;

      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
      }

      .form-group {
        margin-bottom: 1rem;

        label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 600;
          color: #333;

          &.checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;

            input[type="checkbox"] {
              width: auto;
            }
          }
        }

        select,
        input,
        textarea {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;

          &:focus {
            outline: none;
            border-color: vars.$primary-color;
            box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
          }
        }

        textarea {
          resize: vertical;
          min-height: 100px;
        }
      }

      .modal-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;

        .btn {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 6px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;

          &.btn-secondary {
            background: #6c757d;
            color: white;

            &:hover {
              background: #5a6268;
            }
          }

          &.btn-primary {
            background: vars.$primary-color;
            color: white;

            &:hover {
              background: color.adjust(vars.$primary-color, $lightness: -10%);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .notifications-management {
    .notifications-content {
      margin-left: 0;
      padding: 1rem;

      .page-header {
        flex-direction: column;
        align-items: stretch;
      }

      .filters-section {
        flex-direction: column;
        gap: 1rem;

        .filter-group select {
          min-width: 100%;
        }
      }

      .notifications-list .notification-card {
        .card-header {
          flex-direction: column;
          align-items: stretch;
          gap: 1rem;

          .notification-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
          }

          .notification-actions {
            justify-content: flex-start;
          }
        }

        .card-body .notification-stats {
          grid-template-columns: 1fr;
        }
      }

      .notification-stats-summary {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1rem;

          .stat-number {
            font-size: 1.5rem;
          }
        }
      }
    }
  }

  .modal-overlay .modal-content .modal-body .form-row {
    grid-template-columns: 1fr;
  }
}
