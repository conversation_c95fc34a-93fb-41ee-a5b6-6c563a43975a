import { useState, useEffect } from "react";
import { bloodRequestService } from "../services/bloodRequestService";
import { isPendingRequest, isCompletedStatus } from "../utils/bloodRequestStatusUtils";
import authService from "../services/authService";

/**
 * Custom hook to fetch blood request counts for sidebar display
 * - For blood department doctors: Shows PENDING requests (need action)
 * - For other department doctors: Shows COMPLETED requests (their own requests that are ready)
 */
export const useBloodRequestCounts = () => {
  const [counts, setCounts] = useState({
    internal: 0, // Internal requests count
    external: 0, // External requests count
    total: 0, // Total relevant requests
    loading: true,
    error: null,
  });

  const currentUser = authService.getCurrentUser();
  const isBloodDepartment = currentUser?.departmentID === 1;

  // Helper function to get localStorage key for last viewed time
  const getLastViewedKey = () => {
    return `bloodRequestLastViewed_${currentUser?.id}`;
  };

  const fetchCounts = async () => {
    try {
      setCounts((prev) => ({ ...prev, loading: true, error: null }));

      // Fetch all blood requests
      const response = await bloodRequestService.getAllBloodRequests();

      // Check if the response is successful
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch blood requests");
      }

      const allRequests = response.data || [];

      let relevantRequests = [];
      let internalCount = 0;
      let externalCount = 0;

      if (isBloodDepartment) {
        // Blood department doctors: Show PENDING requests that need action
        const internalRequests = allRequests.filter(
          (request) =>
            (request.requestSource === "INTERNAL" ||
              request.source === "internal" ||
              request.userType === "DOCTOR" ||
              !request.requestSource) && // Default to internal if not specified
            isPendingRequest(request)
        );

        const externalRequests = allRequests.filter(
          (request) =>
            (request.requestSource === "EXTERNAL" ||
              request.source === "external" ||
              request.userType === "MEMBER") &&
            isPendingRequest(request)
        );

        internalCount = internalRequests.length;
        externalCount = externalRequests.length;
        relevantRequests = [...internalRequests, ...externalRequests];
      } else {
        // Other department doctors: Show COMPLETED requests that belong to them
        const doctorId = parseInt(currentUser?.id);
        if (doctorId) {
          const lastViewedTime = localStorage.getItem(getLastViewedKey());
          const lastViewed = lastViewedTime ? new Date(lastViewedTime) : null;

          relevantRequests = allRequests.filter((request) => {
            const requestUserId = parseInt(request.userId || request.userID);
            const isOwnRequest = requestUserId === doctorId;
            const isCompleted = isCompletedStatus(request.status);

            // Only show if completed and updated after last viewed time
            if (isOwnRequest && isCompleted) {
              if (!lastViewed) return true; // Show all if never viewed
              // Use updatedTime or createdTime to check when status was changed to completed
              const statusChangeTime = new Date(request.updatedTime || request.createdTime);
              return statusChangeTime > lastViewed;
            }
            return false;
          });
        }

        // For non-blood department, we don't separate internal/external
        internalCount = relevantRequests.length;
        externalCount = 0;
      }

      setCounts({
        internal: internalCount,
        external: externalCount,
        total: relevantRequests.length,
        loading: false,
        error: null,
      });
    } catch (error) {
      console.error("Error fetching blood request counts:", error);
      setCounts((prev) => ({
        ...prev,
        loading: false,
        error: error.message || "Failed to fetch counts",
      }));
    }
  };

  useEffect(() => {
    fetchCounts();

    // Optional: Set up polling to update counts periodically
    const interval = setInterval(fetchCounts, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [currentUser?.id, isBloodDepartment]); // Re-fetch when user or department changes

  return {
    ...counts,
    refetch: fetchCounts,
  };
};
